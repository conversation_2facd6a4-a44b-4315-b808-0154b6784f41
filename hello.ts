// function greet(name: string): string {
//   return `Hello, ${name}!`;
// }

// const message: string = greet("World");
// console.log(message);

// let isActive: boolean = true;
// let hasPermission = false;
// console.log(isActive);
// console.log(hasPermission);

// Number type examples
// let decimal: number = 6;
// let hex: number = 0xf00d;      // Hexadecimal
// let binary: number = 0b1010;   // Binary
// let octal: number = 0o744;     // Octal
// let Name: string = "<PERSON>vya";  
// let lastName: string = "M S"   

// // Output the values
// console.log(decimal);
// console.log(hex);
// console.log(binary);
// console.log(octal);
// console.log(Name);
// console.log(lastName)



// TypeScript infers the shape of the object
// const user = {
// name: "Alice",
// age: 30,
// isAdmin: true,
// email: "<EMAIL>",
// };
// let Name: string = "<PERSON><PERSON><PERSON>";  
// let age: number = 23;
// function add(a: number, b: number) {
// return a + b;
// }
// console.log(add(2,3))

// // TypeScript knows these properties exist
// console.log(user.name);  // OK
// console.log(user.email);
// console.log(Name)
// console.log(age)

// let username: string = "alice";
// let age:number = 42;
// console.log(username);
// console.log(age)

// function add(a: number, b:number) {
// return a + b;
// }

// console.log(add(5, 3));

// 1. JSON.parse returns 'any' because the structure isn't known at compile time
// const data = JSON.parse('{ "name": "Alice", "age": 30 }');
// console.log(data);

// // 2. Variables declared without initialization
// let something;  // Type is 'any'
// let somethings: string = 'hello';
// something = 42;  // No error
// console.log(something);
// console.log(somethings)

// let x:boolean = true
// console.log(x)

// let y:undefined = undefined
// console.log(typeof(y))

// let z:null = null
// console.log(typeof(z))


// const names: string[] = [];  
// names.push("Divya");
// names.push("M S");

// const hello: string[] = [];
// hello.push("Hello")

// console.log(hello)
// console.log(names);

// const numbers = [1, 2, 3]; // inferred to type number[]
// numbers.push(4); // no error
// // comment line below out to see the successful assignment 
// // numbers.push("2"); // Error: Argument of type 'string' is not assignable to parameter of type 'number'.
// let head: number = numbers[3]; // no error
// console.log(head);


// define our tuple
// let ourTuple: [number, boolean, string];
// ourTuple = [5, false, 'Coding God was here'];

// console.log(ourTuple);


// let tupple: [number, boolean, string, number];
// tupple = [3, true, 'Hello and Welcome', 8]
// console.log(tupple)

// const car: {type:string, model:string, year:number}={
//     type:"Tata",
//     model:"XX",
//     year:2021
// }
// console.log(car)


// const car = {
//   type: "Toyota",
// };
// car.type = "Ford"; // no error
// car.type= "Honda"
// console.log(car)

// enum CardinalDirections {
//   North,
//   East,
//   South,
//   West
// };

// // logs 1 since we initialized the first value to something other than '0'
// console.log(CardinalDirections.North);

// // logs 4 as it continued on from the initial '1' value
// console.log(CardinalDirections.East);

// enum StatusCodes {
//   NotFound = 404,
//   Success = 200,
//   Accepted = 202,
//   BadRequest = 400
// };

// // logs 404
// console.log(StatusCodes.Accepted);

// // logs 200
// console.log(StatusCodes.BadRequest);

// enum CardinalDirections {
//   North = 'North',
//   East = "East",
//   South = "South",
//   West = "West"
// };
// // logs "North"
// console.log(CardinalDirections.East);
// // logs "West"
// console.log(CardinalDirections.South);

// // TypeScript infers 'string'
// let username = "alice";

// // TypeScript infers 'number'
// let score = 100;

// // TypeScript infers 'boolean[]'
// let flags = [true, false, true];

// // TypeScript infers return type as 'number'
// function add(a: number, b: number) {
//   return a + b;
// }

// // Log the values to see them in the output
// console.log(username);
// console.log(score);
// console.log(flags);
// console.log(add(5, 3));


// type CarYear = number;
// type CarType = string;
// type CarModel = string;

// type Car = {
//   year: CarYear,
//   type: CarType,
//   model: CarModel
// };
// const carYear: CarYear = 2001
// const carType: CarType = "Toyota"
// const carModel: CarModel = "Corolla"
// const car: Car = {
//   year: carYear,
//   type: carType,
//   model: carModel
// };

// console.log(car);

// function getTime(): number {
//   return new Date().getTime();
// }

// console.log(getTime());


// let x: unknown = '4';
// console.log((x as string).length);

// class Person {
//   name: string;
//   age: number
// }
      
// const person = new Person();
// person.name = "Jane";
// person.age = 20;

// console.log(person);


// const Name: string = "Divya";  
// const age: number = 23;
// const isStudent: boolean = true;
// const hobbies: string[] = ["Reading", "Coding", "Traveling"];
// const role: [string, number] = ["Admin", 1];
// enum Color {Red, Green, Blue};
// const favoriteColor: Color = Color.Blue;
// let anything: any = "Hello";
// anything = 42;
// const u: undefined = undefined;
// console.log(Name, age, isStudent, hobbies, role, favoriteColor, anything, u)


// function add(a: number, b: number): number {
//   return a % b;
// }

// const sum: number = add(2, 3);
// console.log(sum);


// console.log("Number Properties in TypeScript:"); 
// console.log("Maximum value of a number variable has :" 
//                                    + Number.MAX_VALUE); 
// console.log("The least value of a number variable has:" 
//                                     + Number.MIN_VALUE); 
// console.log("Value of Negative Infinity:" 
//                              + Number.NEGATIVE_INFINITY); 
// console.log("Value of Negative Infinity:" 
//                              + Number.POSITIVE_INFINITY);


// let day: number = 0;
// if (day <= 0 || day > 7) {
//     day = Number.NaN;
//     console.log("Day is " + day);
// } else {
//     console.log("Value Accepted..");
// }